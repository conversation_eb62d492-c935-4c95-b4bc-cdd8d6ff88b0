@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Services
@using RazeWinComTr.Areas.Admin.DbModel
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Market"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Market"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/market">@L["Markets"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post" enctype="multipart/form-data">
                <input type="hidden" asp-for="Entity.Id" />
                <input type="hidden" asp-for="Entity.IconUrl" />
                <input type="hidden" asp-for="Entity.OriginalIsApi" />
                <input type="hidden" id="originalIsApi" value="@(Model.Entity.OriginalIsApi ? "true" : "false")" />

                @* Hidden inputs to preserve values when fields are disabled *@
                <input type="hidden" id="hiddenIsApi" asp-for="Entity.IsApi" />
                <input type="hidden" id="hiddenApiServiceName" asp-for="Entity.ApiServiceName" />
                <input type="hidden" id="hiddenCoin" asp-for="Entity.Coin" />
                <input type="hidden" id="hiddenName" asp-for="Entity.Name" />
                <input type="hidden" id="hiddenShortName" asp-for="Entity.ShortName" />
                <input type="hidden" id="hiddenPairCode" asp-for="Entity.PairCode" />
                <input type="hidden" id="hiddenBuyPrice" asp-for="Entity.BuyPrice" />
                <input type="hidden" id="hiddenSellPrice" asp-for="Entity.SellPrice" />
                <div class="card-body">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="Entity.IsActive" class="custom-control-input" />
                            <label asp-for="Entity.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            @if (Model.Entity.OriginalIsApi)
                            {
                                <input asp-for="Entity.IsApi" class="custom-control-input bg-light" id="isApiCheckbox" onclick="return false;" />
                            }
                            else
                            {
                                <input asp-for="Entity.IsApi" class="custom-control-input" id="isApiCheckbox" />
                            }
                            <label asp-for="Entity.IsApi" class="custom-control-label">@L["Is API"]</label>
                            @if (Model.Entity.OriginalIsApi)
                            {
                                <small class="form-text text-muted">@L["This setting cannot be changed once enabled"]</small>
                            }
                        </div>
                    </div>
                    <div class="form-group" id="apiServiceNameGroup" style="display: none;">
                        <label asp-for="Entity.ApiServiceName">@L["API Service"]</label>
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <select asp-for="Entity.ApiServiceName" class="form-control bg-light" id="apiServiceSelect" disabled>
                                <option value="">@L["Select API Service"]</option>
                                <option value="Bitexen">Bitexen</option>
                                <option value="BTCTurk">BTCTurk</option>
                            </select>
                        }
                        else
                        {
                            <select asp-for="Entity.ApiServiceName" class="form-control" id="apiServiceSelect">
                                <option value="">@L["Select API Service"]</option>
                                <option value="Bitexen">Bitexen</option>
                                <option value="BTCTurk">BTCTurk</option>
                            </select>
                        }
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only once API is enabled"]</small>
                        }
                    </div>
                    <div class="form-group" id="apiPairGroup" style="display: none;">
                        <label for="apiPairSelect">@L["API Pair"]</label>
                        <select id="apiPairSelect" class="form-control">
                            <option value="">@L["Select Pair"]</option>
                        </select>
                        <small class="form-text text-muted">@L["Select a pair from the API to automatically fill the Pair Code field"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Coin">@L["Coin Symbol"]</label>
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <input asp-for="Entity.Coin" class="form-control bg-light" required readonly />
                        }
                        else
                        {
                            <input asp-for="Entity.Coin" class="form-control" required />
                        }
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Name">@L["Name"]</label>
                        <input asp-for="Entity.Name" class="form-control @(Model.Entity.OriginalIsApi ? "bg-light" : "")" required @(Model.Entity.OriginalIsApi ? "readonly" : "") />
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.ShortName">@L["Short Name"]</label>
                        <input asp-for="Entity.ShortName" class="form-control @(Model.Entity.OriginalIsApi ? "bg-light" : "")" required @(Model.Entity.OriginalIsApi ? "readonly" : "") />
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.PairCode">@L["Pair Code"]</label>
                        <input asp-for="Entity.PairCode" class="form-control @(Model.Entity.OriginalIsApi ? "bg-light" : "")" required @(Model.Entity.OriginalIsApi ? "readonly" : "") />
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.BuyPrice">@L["Buy Price"]</label>
                        <input asp-for="Entity.BuyPrice" type="text" class="form-control decimal-input @(Model.Entity.OriginalIsApi ? "bg-light" : "")" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" @(Model.Entity.OriginalIsApi ? "readonly" : "") />
                        <span asp-validation-for="Entity.BuyPrice" class="text-danger"></span>
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.SellPrice">@L["Sell Price"]</label>
                        <input asp-for="Entity.SellPrice" type="text" class="form-control decimal-input @(Model.Entity.OriginalIsApi ? "bg-light" : "")" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" @(Model.Entity.OriginalIsApi ? "readonly" : "") />
                        <span asp-validation-for="Entity.SellPrice" class="text-danger"></span>
                        @if (Model.Entity.OriginalIsApi)
                        {
                            <small class="form-text text-muted">@L["This field is read-only when using API"]</small>
                        }
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.DecimalPlaces">@L["Decimal Places"]</label>
                        <input asp-for="Entity.DecimalPlaces" class="form-control" type="number" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MinimumBuy">@L["Minimum Buy"]</label>
                        <input asp-for="Entity.MinimumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MinimumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MaximumBuy">@L["Maximum Buy"]</label>
                        <input asp-for="Entity.MaximumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MaximumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MinimumSell">@L["Minimum Sell"]</label>
                        <input asp-for="Entity.MinimumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MinimumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.MaximumSell">@L["Maximum Sell"]</label>
                        <input asp-for="Entity.MaximumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.MaximumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.GeneralIncrease">@L["General Increase"]</label>
                        <input asp-for="Entity.GeneralIncrease" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="Entity.GeneralIncrease" class="text-danger"></span>
                        <small class="form-text text-muted">@L["This value will be added to the buy and sell prices. Can be negative."]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Order">@L["Order"]</label>
                        <input asp-for="Entity.Order" class="form-control" type="number" required />
                    </div>

                    <div class="form-group">
                        <label asp-for="ImageFile">@L["Icon Image"]</label>
                        <input type="file" asp-for="ImageFile" class="form-control" accept="image/*" />
                        @if (!string.IsNullOrEmpty(Model.Entity.IconUrl))
                        {
                            <img src="@(FileService.getGenericDownloadUrl(nameof(Market), Model.Entity.Id.ToString()))" class="mt-2" style="max-height: 100px" alt="@Model.Entity.Name" />
                        }
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Market" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="/js/decimal-input-handler.js"></script>
    <script>
        $(document).ready(function() {
            // Initial setup is now handled by server-side rendering
            // No need for client-side readonly/disabled setup

            // Function to toggle API service name field visibility and read-only state of fields
            function toggleApiServiceNameField() {
                var isApiChecked = $('#isApiCheckbox').is(':checked');

                if (isApiChecked) {
                    $('#apiServiceNameGroup').show();
                    // Show pair dropdown only if an API service is selected
                    if ($('#apiServiceSelect').val()) {
                        $('#apiPairGroup').show();
                        loadApiPairs($('#apiServiceSelect').val());
                    }

                    // Make API-related fields read-only
                    toggleApiFieldsReadOnly(true);
                } else {
                    $('#apiServiceNameGroup').hide();
                    $('#apiPairGroup').hide();
                    $('#Entity_ApiServiceName').val('');
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');

                    // Make API-related fields editable
                    toggleApiFieldsReadOnly(false);
                }
            }

            // Function to toggle read-only state of API-related fields
            function toggleApiFieldsReadOnly(makeReadOnly) {
                // For new markets (not originally API), we can toggle readonly state
                var originalIsApi = $('#originalIsApi').val() === 'true';

                if (!originalIsApi) {
                    // Only toggle for non-API markets that are being converted to API
                    var apiFields = [
                        '#Entity_Coin',
                        '#Entity_Name',
                        '#Entity_ShortName',
                        '#Entity_PairCode',
                        '#Entity_BuyPrice',
                        '#Entity_SellPrice'
                    ];

                    apiFields.forEach(function(selector) {
                        var $field = $(selector);
                        $field.prop('readonly', makeReadOnly);

                        if (makeReadOnly) {
                            $field.addClass('bg-light');
                            if (!$field.next('.api-readonly-note').length) {
                                $field.after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only when using API"]</small>');
                            }
                        } else {
                            $field.removeClass('bg-light');
                            $field.next('.api-readonly-note').remove();
                        }
                    });

                    // Handle API Service dropdown for new markets
                    var $apiServiceSelect = $('#apiServiceSelect');
                    if (makeReadOnly) {
                        $apiServiceSelect.prop('disabled', true).addClass('bg-light');
                        if (!$apiServiceSelect.next('.api-readonly-note').length) {
                            $apiServiceSelect.after('<small class="form-text text-muted api-readonly-note">@L["This field is read-only once API is enabled"]</small>');
                        }
                    } else {
                        $apiServiceSelect.prop('disabled', false).removeClass('bg-light');
                        $apiServiceSelect.next('.api-readonly-note').remove();
                    }
                }
                // For existing API markets, fields are already rendered as readonly/disabled from server
            }

            // Function to load pairs from the selected API service
            function loadApiPairs(apiServiceName) {
                if (!apiServiceName) {
                    $('#apiPairGroup').hide();
                    return;
                }

                // Show loading indicator
                $('#apiPairSelect').empty().append('<option value="">@L["Loading..."]</option>');
                $('#apiPairGroup').show();

                // Call the API to get the pairs
                $.ajax({
                    url: `/api/ajax/pairs/${apiServiceName}`,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Clear the dropdown
                        $('#apiPairSelect').empty();
                        $('#apiPairSelect').append('<option value="">@L["Select Pair"]</option>');

                        // Add the pairs to the dropdown
                        if (data && data.length > 0) {
                            $.each(data, function(index, pair) {
                                $('#apiPairSelect').append(`<option value="${pair.pairCode}" data-base="${pair.baseCurrency}" data-quote="${pair.quoteCurrency}">${pair.displayName}</option>`);
                            });

                            // If we have a current pair code, select it in the dropdown
                            var currentPairCode = $('#Entity_PairCode').val();
                            if (currentPairCode) {
                                $('#apiPairSelect option[value="' + currentPairCode + '"]').prop('selected', true);
                            }
                        } else {
                            $('#apiPairSelect').append('<option value="">@L["No pairs found"]</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading pairs:', error);
                        $('#apiPairSelect').empty().append('<option value="">@L["Error loading pairs"]</option>');
                    }
                });
            }

            // Function to sync hidden fields before form submission
            function syncHiddenFields() {
                var originalIsApi = $('#originalIsApi').val() === 'true';

                // Always sync IsApi and ApiServiceName
                $('#hiddenIsApi').val($('#isApiCheckbox').is(':checked'));

                // For ApiServiceName, temporarily enable if disabled to get value
                var $apiServiceSelect = $('#apiServiceSelect');
                var wasDisabled = $apiServiceSelect.prop('disabled');
                if (wasDisabled) {
                    $apiServiceSelect.prop('disabled', false);
                }
                $('#hiddenApiServiceName').val($apiServiceSelect.val());
                if (wasDisabled) {
                    $apiServiceSelect.prop('disabled', true);
                }

                // Sync other fields if they are readonly (for existing API markets)
                if (originalIsApi || $('#Entity_Coin').prop('readonly')) {
                    $('#hiddenCoin').val($('#Entity_Coin').val());
                    $('#hiddenName').val($('#Entity_Name').val());
                    $('#hiddenShortName').val($('#Entity_ShortName').val());
                    $('#hiddenPairCode').val($('#Entity_PairCode').val());
                    $('#hiddenBuyPrice').val($('#Entity_BuyPrice').val());
                    $('#hiddenSellPrice').val($('#Entity_SellPrice').val());
                }
            }

            // Sync hidden fields before form submission
            $('form').on('submit', function() {
                syncHiddenFields();
            });

            // Initial state
            toggleApiServiceNameField();

            // On change event for IsApi checkbox
            $('#isApiCheckbox').change(function() {
                toggleApiServiceNameField();
            });

            // On change event for API service select
            $('#apiServiceSelect').change(function() {
                var apiServiceName = $(this).val();
                if (apiServiceName) {
                    loadApiPairs(apiServiceName);
                } else {
                    $('#apiPairGroup').hide();
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');
                }
            });

            // On change event for API pair select
            $('#apiPairSelect').change(function() {
                var selectedPairCode = $(this).val();
                var selectedOption = $(this).find('option:selected');

                if (selectedPairCode) {
                    // Set the pair code field
                    $('#Entity_PairCode').val(selectedPairCode);

                    // If we have data attributes for base and quote currencies, use them to set coin name fields
                    var baseCurrency = selectedOption.data('base');
                    if (baseCurrency) {
                        $('#Entity_Coin').val(baseCurrency);
                    }

                    // Set the name field based on the display name
                    var displayName = selectedOption.text();
                    if (displayName && displayName !== '@L["Select Pair"]') {
                        $('#Entity_Name').val(displayName);
                        $('#Entity_ShortName').val(displayName);
                    }
                }
            });

            // Add custom validation method for decimal inputs with comma
            $.validator.addMethod('decimalComma', function (value, element) {
                // Allow empty values for optional fields
                if (this.optional(element)) {
                    return true;
                }

                // Check if the value matches the pattern for decimal with comma
                return /^-?\d+(?:.\d+)?$/.test(value);
            }, '@L["Please enter a valid number with comma as decimal separator (e.g., 123,45)"]');

            // Apply the validation method to the form
            $.validator.unobtrusive.adapters.add('decimalComma', [], function (options) {
                options.rules['decimalComma'] = true;
                options.messages['decimalComma'] = options.message;
            });

            // Format decimal values on page load
            $('.decimal-input').each(function() {
                var $input = $(this);
                var value = $input.val();

                // Convert dot to comma for display
                if (value) {
                    value = value.toString().replace(',', '.');
                    $input.val(value);
                }
            });
             $('.decimal-input').keyup(function() {
                var $input = $(this);
                var value = $input.val();

                // Convert dot to comma for display
                if (value) {
                    value = value.toString().replace(',', '.');
                    $input.val(value);
                }
            });
        });
    </script>
}
