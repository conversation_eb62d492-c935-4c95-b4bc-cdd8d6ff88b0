using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Tests.Unit.Services;

public class VerificationServiceTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<ILogger<VerificationService>> _mockLogger;
    private readonly VerificationService _verificationService;

    public VerificationServiceTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);
        _mockLogger = new Mock<ILogger<VerificationService>>();
        _verificationService = new VerificationService(_context, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateVerificationTokenAsync_ShouldCreateNewVerification()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Act
        var token = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Assert
        Assert.NotNull(token);
        Assert.NotEmpty(token);

        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId && v.VerificationType == verificationType);

        Assert.NotNull(verification);
        Assert.Equal(token, verification.VerificationToken);
        Assert.Equal(targetValue, verification.TargetValue);
        Assert.False(verification.IsVerified);
        Assert.NotNull(verification.TokenExpiry);
        Assert.True(verification.TokenExpiry > DateTime.UtcNow);
    }

    [Fact]
    public async Task GenerateVerificationTokenAsync_ExistingVerification_ShouldUpdateToken()
    {
        // Arrange
        int userId = 1;
        var verificationType = VerificationType.Email;
        var targetValue = "<EMAIL>";

        // Create existing verification
        var existingVerification = new UserVerification
        {
            UserId = userId,
            VerificationType = verificationType,
            TargetValue = targetValue,
            VerificationToken = "old-token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(existingVerification);
        await _context.SaveChangesAsync();

        // Act
        var newToken = await _verificationService.GenerateVerificationTokenAsync(userId, verificationType, targetValue);

        // Assert
        Assert.NotEqual("old-token", newToken);

        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == userId && v.VerificationType == verificationType);

        Assert.NotNull(verification);
        Assert.Equal(newToken, verification.VerificationToken);
        Assert.Equal(existingVerification.VerificationId, verification.VerificationId);
    }

    [Fact]
    public async Task VerifyTokenAsync_ValidToken_ShouldReturnTrue()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "valid-token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.VerifyTokenAsync("valid-token");

        // Assert
        Assert.True(result);

        var updatedVerification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == "valid-token");
        Assert.NotNull(updatedVerification);
        Assert.True(updatedVerification.IsVerified);
        Assert.NotNull(updatedVerification.VerifiedDate);
    }

    [Fact]
    public async Task VerifyTokenAsync_InvalidToken_ShouldReturnFalse()
    {
        // Act
        var result = await _verificationService.VerifyTokenAsync("invalid-token");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task VerifyTokenAsync_ExpiredToken_ShouldReturnFalse()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "expired-token",
            TokenExpiry = DateTime.UtcNow.AddHours(-1), // Expired
            IsVerified = false,
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.VerifyTokenAsync("expired-token");

        // Assert
        Assert.False(result);

        var updatedVerification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == "expired-token");
        Assert.NotNull(updatedVerification);
        Assert.False(updatedVerification.IsVerified);
    }

    [Fact]
    public async Task VerifyTokenAsync_AlreadyVerifiedToken_ShouldReturnTrue()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "already-verified-token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = true, // Already verified
            VerifiedDate = DateTime.UtcNow.AddMinutes(-30),
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.VerifyTokenAsync("already-verified-token");

        // Assert
        // Should return true to indicate "already verified" status
        Assert.True(result);
    }

    [Fact]
    public async Task IsTokenAlreadyVerifiedAsync_VerifiedToken_ShouldReturnTrue()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "verified-token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.IsTokenAlreadyVerifiedAsync("verified-token");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTokenAlreadyVerifiedAsync_UnverifiedToken_ShouldReturnFalse()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "unverified-token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.IsTokenAlreadyVerifiedAsync("unverified-token");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsVerifiedAsync_VerifiedUser_ShouldReturnTrue()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.IsVerifiedAsync(1, VerificationType.Email);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsVerifiedAsync_UnverifiedUser_ShouldReturnFalse()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.IsVerifiedAsync(1, VerificationType.Email);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_NewUser_ShouldReturnTrue()
    {
        // Act
        var result = await _verificationService.CanSendVerificationAsync(1, VerificationType.Email);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_VerifiedUser_ShouldReturnFalse()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.CanSendVerificationAsync(1, VerificationType.Email);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_RateLimited_ShouldReturnFalse()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            LastAttempt = DateTime.UtcNow.AddMinutes(-2), // 2 minutes ago (less than 5 minutes)
            AttemptsCount = 1,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.CanSendVerificationAsync(1, VerificationType.Email);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanSendVerificationAsync_DailyLimitReached_ShouldReturnFalse()
    {
        // Arrange - Set LastAttempt to today at noon to ensure same date as when test runs
        var today = DateTime.UtcNow.Date;
        var lastAttempt = today.AddHours(12); // Noon today - safe from midnight issues

        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = lastAttempt.AddHours(1),
            IsVerified = false,
            LastAttempt = lastAttempt,
            AttemptsCount = 5, // Daily limit reached
            CrDate = lastAttempt
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.CanSendVerificationAsync(1, VerificationType.Email);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetTimeUntilNextVerificationAsync_RateLimited_ShouldReturnTimeSpan()
    {
        // Arrange
        var lastAttempt = DateTime.UtcNow.AddMinutes(-2); // 2 minutes ago
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            LastAttempt = lastAttempt,
            AttemptsCount = 1,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.GetTimeUntilNextVerificationAsync(1, VerificationType.Email);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Value.TotalMinutes > 0);
        Assert.True(result.Value.TotalMinutes <= 5);
    }

    [Fact]
    public async Task GetTodayAttemptsAsync_ShouldReturnCorrectCount()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = false,
            LastAttempt = DateTime.UtcNow,
            AttemptsCount = 3,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.GetTodayAttemptsAsync(1, VerificationType.Email);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task GetVerificationStatusAsync_ShouldReturnCorrectStatus()
    {
        // Arrange
        var verification = new UserVerification
        {
            UserId = 1,
            VerificationType = VerificationType.Email,
            TargetValue = "<EMAIL>",
            VerificationToken = "token",
            TokenExpiry = DateTime.UtcNow.AddHours(1),
            IsVerified = true,
            VerifiedDate = DateTime.UtcNow,
            LastAttempt = DateTime.UtcNow,
            AttemptsCount = 2,
            CrDate = DateTime.UtcNow
        };
        _context.UserVerifications.Add(verification);
        await _context.SaveChangesAsync();

        // Act
        var result = await _verificationService.GetVerificationStatusAsync(1, VerificationType.Email);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsVerified);
        Assert.Equal("<EMAIL>", result.TargetValue);
        Assert.Equal(VerificationType.Email, result.VerificationType);
        Assert.Equal(2, result.TodayAttempts);
        Assert.Equal(5, result.MaxDailyAttempts);
        Assert.Equal(5, result.MinutesBetweenAttempts);
        Assert.False(result.CanSendVerification); // Verified users can't send more emails
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
